import axios from 'axios';
import { LoginData, RegisterData, TokenResponse, User } from '@mtbrmg/shared';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Helper function to safely get token
const getAccessToken = () => {
  try {
    return localStorage.getItem('access_token');
  } catch (error) {
    console.error('Error accessing localStorage:', error);
    return null;
  }
};

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // Don't try to refresh token for logout requests
    if (error.response?.status === 401 && !originalRequest._retry && !originalRequest.url?.includes('/auth/logout/')) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('refresh_token');
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh/`, {
            refresh: refreshToken,
          });

          const { access } = response.data;
          localStorage.setItem('access_token', access);

          // Retry the original request
          originalRequest.headers.Authorization = `Bearer ${access}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: async (data: LoginData): Promise<TokenResponse> => {
    // Transform email to username for backend compatibility
    // Backend expects { username, password } but frontend sends { email, password }
    const loginPayload = {
      username: data.email.split('@')[0], // Extract username from email (e.g., <EMAIL> -> founder)
      password: data.password
    };

    console.log('Login attempt:', { email: data.email, username: loginPayload.username });

    const response = await api.post('/auth/login/', loginPayload);
    return response.data;
  },

  register: async (data: RegisterData): Promise<TokenResponse> => {
    const response = await api.post('/auth/register/', data);
    return response.data;
  },

  logout: async (): Promise<void> => {
    try {
      // Get refresh token to send for blacklisting
      const refreshToken = localStorage.getItem('refresh_token');

      // Send refresh token to backend for blacklisting
      if (refreshToken) {
        await api.post('/auth/logout/', {
          refresh: refreshToken,
        });
      } else {
        // If no refresh token, still call the endpoint
        await api.post('/auth/logout/', {});
      }
    } catch (error) {
      // Log the error but don't throw it - we still want to clear local storage
      console.error('Logout API error:', error);
    } finally {
      // Always clear local storage regardless of API call success
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
    }
  },

  getProfile: async (): Promise<User> => {
    const response = await api.get('/auth/profile/');
    return response.data;
  },

  refreshToken: async (refreshToken: string): Promise<{ access: string }> => {
    const response = await api.post('/auth/refresh/', {
      refresh: refreshToken,
    });
    return response.data;
  },
};

// Users API
export const usersAPI = {
  getUsers: async (params?: any) => {
    const response = await api.get('/users/', { params });
    return response.data;
  },

  getUser: async (id: string): Promise<User> => {
    const response = await api.get(`/users/${id}/`);
    return response.data;
  },

  updateUser: async (id: string, data: Partial<User>): Promise<User> => {
    const response = await api.patch(`/users/${id}/`, data);
    return response.data;
  },

  deleteUser: async (id: string): Promise<void> => {
    await api.delete(`/users/${id}/`);
  },
};

// Clients API
export const clientsAPI = {
  getClients: async (params?: any) => {
    const response = await api.get('/clients/', { params });
    return response.data;
  },

  getClient: async (id: string) => {
    const response = await api.get(`/clients/${id}/`);
    return response.data;
  },

  createClient: async (data: any) => {
    const response = await api.post('/clients/', data);
    return response.data;
  },

  updateClient: async (id: string, data: any) => {
    const response = await api.patch(`/clients/${id}/`, data);
    return response.data;
  },

  deleteClient: async (id: string): Promise<void> => {
    await api.delete(`/clients/${id}/`);
  },
};

// Projects API
export const projectsAPI = {
  getProjects: async (params?: any) => {
    const response = await api.get('/projects/', { params });
    return response.data;
  },

  getProject: async (id: string) => {
    const response = await api.get(`/projects/${id}/`);
    return response.data;
  },

  createProject: async (data: any) => {
    const response = await api.post('/projects/', data);
    return response.data;
  },

  updateProject: async (id: string, data: any) => {
    const response = await api.patch(`/projects/${id}/`, data);
    return response.data;
  },

  deleteProject: async (id: string): Promise<void> => {
    await api.delete(`/projects/${id}/`);
  },
};

// Tasks API
export const tasksAPI = {
  getTasks: async (params?: any) => {
    const response = await api.get('/tasks/', { params });
    return response.data;
  },

  getTask: async (id: string) => {
    const response = await api.get(`/tasks/${id}/`);
    return response.data;
  },

  createTask: async (data: any) => {
    const response = await api.post('/tasks/', data);
    return response.data;
  },

  updateTask: async (id: string, data: any) => {
    const response = await api.patch(`/tasks/${id}/`, data);
    return response.data;
  },

  deleteTask: async (id: string): Promise<void> => {
    await api.delete(`/tasks/${id}/`);
  },

  logTime: async (taskId: string, data: any) => {
    const response = await api.post(`/tasks/${taskId}/time-logs/`, data);
    return response.data;
  },

  addComment: async (taskId: string, data: any) => {
    const response = await api.post(`/tasks/${taskId}/comments/`, data);
    return response.data;
  },
};

// Team API
export const teamAPI = {
  getTeamMembers: async (params?: any) => {
    const response = await api.get('/team/', { params });
    return response.data;
  },

  getTeamMember: async (id: string) => {
    const response = await api.get(`/team/${id}/`);
    return response.data;
  },

  createTeamMember: async (data: any) => {
    const response = await api.post('/team/', data);
    return response.data;
  },

  updateTeamMember: async (id: string, data: any) => {
    const response = await api.patch(`/team/${id}/`, data);
    return response.data;
  },

  deleteTeamMember: async (id: string): Promise<void> => {
    await api.delete(`/team/${id}/`);
  },

  getTeamStats: async () => {
    const response = await api.get('/team/stats/');
    return response.data;
  },

  updatePerformance: async (id: string, data: any) => {
    const response = await api.post(`/team/${id}/update_performance/`, data);
    return response.data;
  },

  getPerformanceHistory: async (id: string) => {
    const response = await api.get(`/team/${id}/performance_history/`);
    return response.data;
  },

  getDepartments: async () => {
    const response = await api.get('/team/departments/');
    return response.data;
  },

  getRecentHires: async () => {
    const response = await api.get('/team/recent_hires/');
    return response.data;
  },
};

export default api;
